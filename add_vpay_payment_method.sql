-- Add Vpay payment method to the payments table
-- Run this SQL script in your database to add Vpay as a payment option

-- Check if Vpay payment method already exists
SET @vpay_exists = 0;
SELECT COUNT(*) INTO @vpay_exists 
FROM payments 
WHERE type = 'vpay';

-- Insert Vpay payment method if it doesn't exist
SET @sql = IF(@vpay_exists = 0, 
    "INSERT INTO payments (
        ids, 
        type, 
        name, 
        params, 
        min, 
        max, 
        new_users, 
        status, 
        sort, 
        created, 
        changed
    ) VALUES (
        CONCAT(SUBSTRING(MD5(RAND()), 1, 32)), 
        'vpay', 
        'Vpay', 
        '{\"type\":\"vpay\",\"min\":\"10\",\"max\":\"10000\",\"new_users\":\"1\",\"status\":\"0\",\"option\":{\"environment\":\"sandbox\",\"public_key\":\"\",\"secret_key\":\"\",\"tnx_fee\":\"0\",\"rate_to_usd\":\"1540\",\"take_fee_from_user\":\"0\"}}', 
        10, 
        10000, 
        1, 
        0, 
        (SELECT COALESCE(MAX(sort), 0) + 1 FROM payments p WHERE p.type != 'vpay'), 
        NOW(), 
        NOW()
    )", 
    'SELECT "Vpay payment method already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the result
SELECT 
    id,
    type,
    name,
    min,
    max,
    new_users,
    status,
    sort,
    created
FROM payments 
WHERE type = 'vpay';

-- Show all payment methods for reference
SELECT 
    id,
    type,
    name,
    status,
    sort
FROM payments 
ORDER BY sort;
