-- Add Vpay payment method to the payments table
-- Run this SQL script in your database to add Vpay as a payment option
-- This script handles both 'payments' and 'payments_method' table names

-- First, determine which table exists
SET @table_name = '';
SET @table_exists = 0;

-- Check for 'payments' table
SELECT COUNT(*) INTO @table_exists
FROM information_schema.tables
WHERE table_schema = DATABASE()
AND table_name = 'payments';

SET @table_name = IF(@table_exists > 0, 'payments', '');

-- If 'payments' doesn't exist, check for 'payments_method'
IF @table_name = '' THEN
    SELECT COUNT(*) INTO @table_exists
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    AND table_name = 'payments_method';

    SET @table_name = IF(@table_exists > 0, 'payments_method', '');
END IF;

-- Exit if no table found
IF @table_name = '' THEN
    SELECT 'Error: Neither payments nor payments_method table found!' as error_message;
ELSE
    SELECT CONCAT('Using table: ', @table_name) as info_message;

    -- Check if Vpay payment method already exists
    SET @vpay_exists = 0;
    SET @check_sql = CONCAT('SELECT COUNT(*) INTO @vpay_exists FROM ', @table_name, ' WHERE type = "vpay"');
    PREPARE stmt FROM @check_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- Check if 'ids' column exists in the table
    SET @ids_exists = 0;
    SELECT COUNT(*) INTO @ids_exists
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = @table_name
    AND column_name = 'ids';

    -- Check if 'created' column exists in the table
    SET @created_exists = 0;
    SELECT COUNT(*) INTO @created_exists
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = @table_name
    AND column_name = 'created';

    -- Check if 'changed' column exists in the table
    SET @changed_exists = 0;
    SELECT COUNT(*) INTO @changed_exists
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = @table_name
    AND column_name = 'changed';

    -- Build INSERT statement based on available columns
    IF @vpay_exists = 0 THEN
        SET @columns = 'type, name, params, min, max, new_users, status, sort';
        SET @values = '"vpay", "Vpay", \'{"type":"vpay","min":"10","max":"10000","new_users":"1","status":"0","option":{"environment":"sandbox","public_key":"","secret_key":"","tnx_fee":"0","rate_to_usd":"1540","take_fee_from_user":"0"}}\', 10, 10000, 1, 0';

        -- Add sort value
        SET @sort_sql = CONCAT('SELECT COALESCE(MAX(sort), 0) + 1 FROM ', @table_name);
        SET @values = CONCAT(@values, ', (', @sort_sql, ')');

        -- Add optional columns
        IF @ids_exists > 0 THEN
            SET @columns = CONCAT('ids, ', @columns);
            SET @values = CONCAT('SUBSTRING(MD5(RAND()), 1, 32), ', @values);
        END IF;

        IF @created_exists > 0 THEN
            SET @columns = CONCAT(@columns, ', created');
            SET @values = CONCAT(@values, ', NOW()');
        END IF;

        IF @changed_exists > 0 THEN
            SET @columns = CONCAT(@columns, ', changed');
            SET @values = CONCAT(@values, ', NOW()');
        END IF;

        SET @insert_sql = CONCAT('INSERT INTO ', @table_name, ' (', @columns, ') VALUES (', @values, ')');
    ELSE
        SET @insert_sql = 'SELECT "Vpay payment method already exists" as message';
    END IF;
END IF;

-- Execute the INSERT statement
IF @table_name != '' THEN
    PREPARE stmt FROM @insert_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- Show the result for Vpay
    SET @select_sql = CONCAT('SELECT id, type, name, min, max, new_users, status, sort FROM ', @table_name, ' WHERE type = "vpay"');
    PREPARE stmt FROM @select_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- Show all payment methods for reference
    SET @select_all_sql = CONCAT('SELECT id, type, name, status, sort FROM ', @table_name, ' ORDER BY sort');
    PREPARE stmt FROM @select_all_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END IF;
