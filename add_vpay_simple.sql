-- Simple Vpay Payment Method Installation Script
-- Try this if the complex script doesn't work
-- Manually run the appropriate INSERT statement based on your table structure

-- First, check your table structure:
-- DESCRIBE payments;
-- OR
-- DESCRIBE payments_method;

-- Option 1: If your table is named 'payments' and has 'ids', 'created', 'changed' columns
INSERT INTO payments (
    ids, 
    type, 
    name, 
    params, 
    min, 
    max, 
    new_users, 
    status, 
    sort, 
    created, 
    changed
) VALUES (
    SUBSTRING(MD5(RAND()), 1, 32), 
    'vpay', 
    'Vpay', 
    '{"type":"vpay","min":"10","max":"10000","new_users":"1","status":"0","option":{"environment":"sandbox","public_key":"","secret_key":"","tnx_fee":"0","rate_to_usd":"1540","take_fee_from_user":"0"}}', 
    10, 
    10000, 
    1, 
    0, 
    (SELECT COALESCE(MAX(sort), 0) + 1 FROM payments p WHERE p.type != 'vpay'), 
    NOW(), 
    NOW()
);

-- Option 2: If your table is named 'payments' and does NOT have 'ids', 'created', 'changed' columns
-- INSERT INTO payments (
--     type, 
--     name, 
--     params, 
--     min, 
--     max, 
--     new_users, 
--     status, 
--     sort
-- ) VALUES (
--     'vpay', 
--     'Vpay', 
--     '{"type":"vpay","min":"10","max":"10000","new_users":"1","status":"0","option":{"environment":"sandbox","public_key":"","secret_key":"","tnx_fee":"0","rate_to_usd":"1540","take_fee_from_user":"0"}}', 
--     10, 
--     10000, 
--     1, 
--     0, 
--     (SELECT COALESCE(MAX(sort), 0) + 1 FROM payments p WHERE p.type != 'vpay')
-- );

-- Option 3: If your table is named 'payments_method' and has 'ids', 'created', 'changed' columns
-- INSERT INTO payments_method (
--     ids, 
--     type, 
--     name, 
--     params, 
--     min, 
--     max, 
--     new_users, 
--     status, 
--     sort, 
--     created, 
--     changed
-- ) VALUES (
--     SUBSTRING(MD5(RAND()), 1, 32), 
--     'vpay', 
--     'Vpay', 
--     '{"type":"vpay","min":"10","max":"10000","new_users":"1","status":"0","option":{"environment":"sandbox","public_key":"","secret_key":"","tnx_fee":"0","rate_to_usd":"1540","take_fee_from_user":"0"}}', 
--     10, 
--     10000, 
--     1, 
--     0, 
--     (SELECT COALESCE(MAX(sort), 0) + 1 FROM payments_method p WHERE p.type != 'vpay'), 
--     NOW(), 
--     NOW()
-- );

-- Option 4: If your table is named 'payments_method' and does NOT have 'ids', 'created', 'changed' columns
-- INSERT INTO payments_method (
--     type, 
--     name, 
--     params, 
--     min, 
--     max, 
--     new_users, 
--     status, 
--     sort
-- ) VALUES (
--     'vpay', 
--     'Vpay', 
--     '{"type":"vpay","min":"10","max":"10000","new_users":"1","status":"0","option":{"environment":"sandbox","public_key":"","secret_key":"","tnx_fee":"0","rate_to_usd":"1540","take_fee_from_user":"0"}}', 
--     10, 
--     10000, 
--     1, 
--     0, 
--     (SELECT COALESCE(MAX(sort), 0) + 1 FROM payments_method p WHERE p.type != 'vpay')
-- );

-- Check the result (use the correct table name)
SELECT id, type, name, status, sort FROM payments WHERE type = 'vpay';
-- OR
-- SELECT id, type, name, status, sort FROM payments_method WHERE type = 'vpay';

-- Show all payment methods
SELECT id, type, name, status, sort FROM payments ORDER BY sort;
-- OR
-- SELECT id, type, name, status, sort FROM payments_method ORDER BY sort;
