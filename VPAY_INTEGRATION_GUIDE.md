# Vpay Payment Gateway Integration Guide

## Overview
This guide covers the complete integration of Vpay payment gateway into your application, similar to the existing Paystack implementation.

## Files Created/Modified

### 1. Controller
- **File**: `app/modules/add_funds/controllers/vpay.php`
- **Purpose**: Main controller handling payment processing, callbacks, and webhooks
- **Key Methods**:
  - `process()`: Handles payment initialization
  - `callback()`: Handles user return from payment
  - `webhook()`: Handles Vpay webhook notifications

### 2. Views
- **File**: `app/modules/add_funds/views/vpay/index.php`
- **Purpose**: Main payment form for users
- **Features**: Amount input, fee calculation, agreement checkbox

- **File**: `app/modules/add_funds/views/vpay/vpay_form.php`
- **Purpose**: Payment processing form with Vpay integration
- **Features**: Dynamic Vpay script loading, payment initialization

### 3. Admin Integration
- **File**: `app/modules/admin/views/payments/integrations/vpay.php`
- **Purpose**: Admin configuration interface
- **Settings**: API keys, environment, fees, currency rates

### 4. Database Migration
- **File**: `app/database/migrations/005_add_vpay_payment_method.php`
- **Purpose**: Adds Vpay payment method to database
- **Alternative**: `install_vpay.php` for manual installation

### 5. Assets
- **Files**: `assets/images/payments/vpay.svg`, `assets/images/payments/vpay.png`
- **Purpose**: Vpay logo for payment interface

## Installation Steps

### Step 1: Database Setup
Run one of the following:

**Option A: Using Migration (if PHP CLI available)**
```bash
php index.php migrate
```

**Option B: Using Installation Script**
1. Access `yoursite.com/install_vpay.php` in your browser
2. Follow the installation prompts
3. Delete the installation file after completion

**Option C: Manual SQL**
```sql
-- Run the SQL from add_vpay_payment_method.sql
```

### Step 2: Admin Configuration
1. Go to Admin Panel → Payments
2. Find "Vpay" in the payment methods list
3. Click "Edit" to configure:
   - **Environment**: Choose "sandbox" for testing, "live" for production
   - **Public Key**: Your Vpay public key from dashboard
   - **Secret Key**: Your Vpay secret key from dashboard
   - **Transaction Fee**: Fee amount in NGN
   - **Currency Rate**: NGN to USD conversion rate
   - **Take Fee from User**: Whether user pays the fee
4. Set **Status** to "Active" when ready

### Step 3: Webhook Configuration
In your Vpay merchant dashboard:
1. Go to Settings → Webhooks
2. Set webhook URL to: `yoursite.com/add_funds/vpay/webhook`
3. Ensure webhook is enabled

## Testing

### Sandbox Testing
1. Set environment to "sandbox"
2. Use Vpay sandbox credentials
3. Test payment flow:
   - User selects Vpay payment
   - Enters amount and agrees to terms
   - Redirected to Vpay payment interface
   - Completes payment (use test cards)
   - Returns to success page
   - Webhook processes payment confirmation

### Live Testing
1. Set environment to "live"
2. Use live Vpay credentials
3. Test with small amounts first
4. Monitor webhook logs

## Configuration Options

### Environment Settings
- **Sandbox**: `https://dropin-sandbox.vpay.africa`
- **Live**: `https://dropin.vpay.africa`

### Supported Features
- ✅ Card payments
- ✅ Bank transfers
- ✅ Webhook notifications
- ✅ Transaction fees
- ✅ Currency conversion
- ✅ User fee options
- ✅ Admin configuration
- ✅ Transaction logging

### Currency Support
- Primary: NGN (Nigerian Naira)
- Conversion: NGN to USD

## Security Features

### Webhook Verification
- JWT token verification in `x-payload-auth` header
- Secret key validation
- IP whitelisting support (configure in Vpay dashboard)

### Transaction Security
- Unique transaction references
- Database transaction logging
- Status verification before fund addition
- Error logging and monitoring

## Troubleshooting

### Common Issues

1. **Payment not completing**
   - Check webhook URL configuration
   - Verify API keys are correct
   - Check webhook logs for errors

2. **Webhook not receiving**
   - Ensure webhook URL is accessible
   - Check firewall settings
   - Verify SSL certificate if using HTTPS

3. **JavaScript errors**
   - Check browser console for errors
   - Ensure Vpay script loads correctly
   - Verify domain configuration

### Debug Mode
Enable debug logging in your application to monitor:
- Payment initialization
- Webhook payloads
- Transaction status updates
- Error messages

## Support

### Vpay Documentation
- Main docs: [Vpay Documentation](https://docs.vpay.africa)
- Webhook guide: See included documentation
- API reference: Available in merchant dashboard

### Integration Support
- Check application logs for errors
- Monitor webhook delivery in Vpay dashboard
- Test in sandbox environment first
- Contact Vpay support for API issues

## Security Recommendations

1. **Never expose secret keys** in frontend code
2. **Always verify webhook signatures**
3. **Use HTTPS** for webhook endpoints
4. **Implement IP whitelisting** when possible
5. **Monitor transaction logs** regularly
6. **Keep API credentials secure**

## Maintenance

### Regular Tasks
- Monitor webhook delivery success rates
- Check transaction completion rates
- Update currency conversion rates
- Review error logs
- Test payment flow periodically

### Updates
- Keep Vpay integration updated with API changes
- Monitor Vpay announcements for updates
- Test after any application updates
- Backup payment configuration before changes
