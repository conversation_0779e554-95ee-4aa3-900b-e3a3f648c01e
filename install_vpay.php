<?php
/**
 * Vpay Payment Gateway Installation Script
 * Run this script once to install Vpay payment method
 * Access via: yoursite.com/install_vpay.php
 */

// Database configuration - update these with your actual database credentials
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'surelogger1'; // Update with your actual database name

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h1>Vpay Payment Gateway Installation</h1>";
    echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

    // First, let's check the actual table structure
    echo "<h2>1. Database Table Structure Check</h2>";

    // Check if payments table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'payments'");
    $stmt->execute();
    $table_exists = $stmt->fetchColumn();

    if (!$table_exists) {
        echo "<p class='error'>❌ 'payments' table not found. Checking for 'payments_method' table...</p>";

        $stmt = $pdo->prepare("SHOW TABLES LIKE 'payments_method'");
        $stmt->execute();
        $table_exists = $stmt->fetchColumn();

        if ($table_exists) {
            $table_name = 'payments_method';
            echo "<p class='info'>✅ Found 'payments_method' table instead.</p>";
        } else {
            echo "<p class='error'>❌ Neither 'payments' nor 'payments_method' table found!</p>";
            exit;
        }
    } else {
        $table_name = 'payments';
        echo "<p class='success'>✅ Found 'payments' table.</p>";
    }

    // Get table structure
    $stmt = $pdo->prepare("DESCRIBE $table_name");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>Table Structure for '$table_name':</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";

    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column['Field'];
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check if Vpay payment method already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM $table_name WHERE type = 'vpay'");
    $stmt->execute();
    $exists = $stmt->fetchColumn();

    echo "<h2>2. Vpay Payment Method Installation</h2>";

    if ($exists > 0) {
        echo "<p class='info'>✅ Vpay payment method already exists in the database.</p>";
    } else {
        // Get the next sort order
        $stmt = $pdo->prepare("SELECT COALESCE(MAX(sort), 0) + 1 as next_sort FROM $table_name");
        $stmt->execute();
        $next_sort = $stmt->fetchColumn();

        // Prepare the parameters JSON
        $params = json_encode([
            'type' => 'vpay',
            'min' => '10',
            'max' => '10000',
            'new_users' => '1',
            'status' => '0',
            'option' => [
                'environment' => 'sandbox',
                'public_key' => '',
                'secret_key' => '',
                'tnx_fee' => '0',
                'rate_to_usd' => '1540',
                'take_fee_from_user' => '0'
            ]
        ]);

        // Build INSERT query based on available columns
        $insert_columns = ['type', 'name', 'params', 'min', 'max', 'new_users', 'status', 'sort'];
        $insert_values = [':type', ':name', ':params', ':min', ':max', ':new_users', ':status', ':sort'];
        $execute_params = [
            ':type' => 'vpay',
            ':name' => 'Vpay',
            ':params' => $params,
            ':min' => 10,
            ':max' => 10000,
            ':new_users' => 1,
            ':status' => 0,
            ':sort' => $next_sort
        ];

        // Add optional columns if they exist
        if (in_array('ids', $column_names)) {
            $insert_columns[] = 'ids';
            $insert_values[] = ':ids';
            $execute_params[':ids'] = substr(md5(uniqid(rand(), true)), 0, 32);
        }

        if (in_array('created', $column_names)) {
            $insert_columns[] = 'created';
            $insert_values[] = ':created';
            $execute_params[':created'] = date('Y-m-d H:i:s');
        }

        if (in_array('changed', $column_names)) {
            $insert_columns[] = 'changed';
            $insert_values[] = ':changed';
            $execute_params[':changed'] = date('Y-m-d H:i:s');
        }

        // Build and execute the INSERT query
        $sql = "INSERT INTO $table_name (" . implode(', ', $insert_columns) . ") VALUES (" . implode(', ', $insert_values) . ")";
        echo "<p class='info'>Executing SQL: $sql</p>";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($execute_params);

        if ($result) {
            echo "<p class='success'>✅ Vpay payment method added successfully!</p>";
        } else {
            echo "<p class='error'>❌ Failed to add Vpay payment method.</p>";
            $errorInfo = $stmt->errorInfo();
            echo "<p class='error'>Error: " . $errorInfo[2] . "</p>";
        }
    }

    // Show current payment methods
    echo "<h2>3. Current Payment Methods:</h2>";
    $stmt = $pdo->prepare("SELECT id, type, name, status, sort FROM $table_name ORDER BY sort");
    $stmt->execute();
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Type</th><th>Name</th><th>Status</th><th>Sort</th></tr>";
    foreach ($payments as $payment) {
        $status_text = $payment['status'] ? 'Active' : 'Inactive';
        $status_class = $payment['status'] ? 'success' : 'error';
        $row_class = $payment['type'] === 'vpay' ? 'style="background-color: #e6f3ff;"' : '';
        echo "<tr $row_class>";
        echo "<td>{$payment['id']}</td>";
        echo "<td>{$payment['type']}</td>";
        echo "<td>{$payment['name']}</td>";
        echo "<td class='$status_class'>$status_text</td>";
        echo "<td>{$payment['sort']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<h2>4. Next Steps:</h2>";
    echo "<ol>";
    echo "<li>Go to your admin panel -> Payments section</li>";
    echo "<li>Find the Vpay payment method and click edit</li>";
    echo "<li>Configure your Vpay API keys and settings</li>";
    echo "<li>Set the status to Active when ready</li>";
    echo "<li>Test the payment flow</li>";
    echo "</ol>";

    echo "<h2>5. Configuration Details:</h2>";
    echo "<ul>";
    echo "<li><strong>Webhook URL:</strong> yoursite.com/add_funds/vpay/webhook</li>";
    echo "<li><strong>Callback URL:</strong> yoursite.com/add_funds/vpay/callback</li>";
    echo "<li><strong>Supported Currency:</strong> NGN (Nigerian Naira)</li>";
    echo "<li><strong>Environment:</strong> Sandbox (for testing) / Live (for production)</li>";
    echo "</ul>";

    echo "<p class='info'><strong>Important:</strong> Delete this file (install_vpay.php) after installation for security.</p>";

} catch (PDOException $e) {
    echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in this file.</p>";
    echo "<p><strong>Error Details:</strong> " . $e->getCode() . " - " . $e->getMessage() . "</p>";
}
?>
