<?php
/**
 * Vpay Payment Gateway Installation Script
 * Run this script once to install Vpay payment method
 * Access via: yoursite.com/install_vpay.php
 */

// Database configuration - update these with your actual database credentials
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'surelogger1'; // Update with your actual database name

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Vpay Payment Gateway Installation</h1>";
    echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
    
    // Check if Vpay payment method already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM payments WHERE type = 'vpay'");
    $stmt->execute();
    $exists = $stmt->fetchColumn();
    
    if ($exists > 0) {
        echo "<p class='info'>✅ Vpay payment method already exists in the database.</p>";
    } else {
        // Get the next sort order
        $stmt = $pdo->prepare("SELECT COALESCE(MAX(sort), 0) + 1 as next_sort FROM payments");
        $stmt->execute();
        $next_sort = $stmt->fetchColumn();
        
        // Prepare the parameters JSON
        $params = json_encode([
            'type' => 'vpay',
            'min' => '10',
            'max' => '10000',
            'new_users' => '1',
            'status' => '0',
            'option' => [
                'environment' => 'sandbox',
                'public_key' => '',
                'secret_key' => '',
                'tnx_fee' => '0',
                'rate_to_usd' => '1540',
                'take_fee_from_user' => '0'
            ]
        ]);
        
        // Generate unique ID
        $ids = substr(md5(uniqid(rand(), true)), 0, 32);
        
        // Insert Vpay payment method
        $stmt = $pdo->prepare("
            INSERT INTO payments (
                ids, type, name, params, min, max, new_users, status, sort, created, changed
            ) VALUES (
                :ids, :type, :name, :params, :min, :max, :new_users, :status, :sort, :created, :changed
            )
        ");
        
        $result = $stmt->execute([
            ':ids' => $ids,
            ':type' => 'vpay',
            ':name' => 'Vpay',
            ':params' => $params,
            ':min' => 10,
            ':max' => 10000,
            ':new_users' => 1,
            ':status' => 0,
            ':sort' => $next_sort,
            ':created' => date('Y-m-d H:i:s'),
            ':changed' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            echo "<p class='success'>✅ Vpay payment method added successfully!</p>";
        } else {
            echo "<p class='error'>❌ Failed to add Vpay payment method.</p>";
        }
    }
    
    // Show current payment methods
    echo "<h2>Current Payment Methods:</h2>";
    $stmt = $pdo->prepare("SELECT id, type, name, status, sort FROM payments ORDER BY sort");
    $stmt->execute();
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Type</th><th>Name</th><th>Status</th><th>Sort</th></tr>";
    foreach ($payments as $payment) {
        $status_text = $payment['status'] ? 'Active' : 'Inactive';
        $status_class = $payment['status'] ? 'success' : 'error';
        echo "<tr>";
        echo "<td>{$payment['id']}</td>";
        echo "<td>{$payment['type']}</td>";
        echo "<td>{$payment['name']}</td>";
        echo "<td class='$status_class'>$status_text</td>";
        echo "<td>{$payment['sort']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Next Steps:</h2>";
    echo "<ol>";
    echo "<li>Go to your admin panel -> Payments section</li>";
    echo "<li>Find the Vpay payment method and click edit</li>";
    echo "<li>Configure your Vpay API keys and settings</li>";
    echo "<li>Set the status to Active when ready</li>";
    echo "<li>Test the payment flow</li>";
    echo "</ol>";
    
    echo "<p class='info'><strong>Important:</strong> Delete this file (install_vpay.php) after installation for security.</p>";
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in this file.</p>";
}
?>
