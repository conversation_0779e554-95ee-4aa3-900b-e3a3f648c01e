<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="vpayGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="200" height="60" rx="8" fill="url(#vpayGradient)"/>
  
  <!-- V Letter -->
  <path d="M20 15 L35 45 L50 15" stroke="white" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Pay Text -->
  <text x="65" y="38" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">Pay</text>
  
  <!-- Subtitle -->
  <text x="130" y="25" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">SECURE</text>
  <text x="130" y="38" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">PAYMENTS</text>
  
  <!-- Decorative elements -->
  <circle cx="175" cy="20" r="3" fill="rgba(255,255,255,0.6)"/>
  <circle cx="185" cy="25" r="2" fill="rgba(255,255,255,0.4)"/>
  <circle cx="175" cy="40" r="2" fill="rgba(255,255,255,0.4)"/>
</svg>
