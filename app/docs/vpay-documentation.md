---
description: >-
  This section details the setup of the VPay JS Payment Dropin for desktop and
  mobile responsive web apps. All examples are made with reference to the
  SANDBOX environment.
---

# 3. Web App Setup



With a few lines of code, you can integrate the VPay Javascript Dropin library and start accepting payments on your web and mobile web sites. The library is available at:



_Sandbox Base URL:_ [_https://dropin-sandbox.vpay.africa_](https://dropin-sandbox.vpay.africa)

_Live Base URL:_ [_https://dropin.vpay.africa_](https://dropin.vpay.africa)



### HTML Code Snippet (For Sandbox)



```html
<!DOCTYPE html>
    <head>
        
<meta name="viewport" content="width=device-width, initial-scale=1.0">
        
<title>Order Checkout Page</title>
        <script src="{{baseURL}}/dropin/v1/initialise.js"></script>
        <!-- Use appropriate Base URL as described above -->
    </head>
    
    <body>
        <!--Your HTML Content goes here-->
        
        <script>
            (() => {
                const options = {
                    amount: 119,
                    currency: 'NGN',
                    domain: 'sandbox',
                    key: 'fdcdb195-6553-4890-844c-ee576b7ea715',
                    
email: '<EMAIL>',
                    transactionref: 'z31zs098zas8w3774h44344f8yg',
                    customer_logo:
'https://www.vpay.africa/static/media/vpayLogo.91e11322.svg',
                    customer_service_channel: '+2348030007000, <EMAIL>',
                    txn_charge: 6,
                    txn_charge_type: 'flat',
                    onSuccess: function(response) { console.log('Hello World!',
 response.message); },
                    onExit: function(response) { console.log('Hello World!',
 response.message); }
                }
                
                if(window.VPayDropin){
                    const {open, exit} = VPayDropin.create(options);
                    open();                    
                }                
            })();
        </script>
    </body>
</html>
```



{% hint style="warning" %}
Each transactionref that you generate should be unique, alphanumeric and different from previously used ones. You may generate a ref using any scheme of your preference.
{% endhint %}

{% hint style="warning" %}
When setting up for your live web app, remember to edit the **head** \<script> reference from the SANDBOX library to the LIVE library i.e. _https://dropin.vpay.africa/dropin/v1/initialise.js_&#x20;
{% endhint %}

---
description: >-
  This page contains a breakdown of the configuration options that can be passed
  to control the behaviour of the VPay Javascript Payment Dropin
---

# 5. Config Options



The dropin JS library options are a set of parameters passed into the `create()` function as a JSON payload (as seen from samples in previous pages). Below, each available option is described in detail.



<table><thead><tr><th width="82">S/N</th><th width="168">Name</th><th width="458">Description</th><th width="233">Relevance</th></tr></thead><tbody><tr><td>1</td><td>amount</td><td>This is the order amount (as it would be represented in note denomination, <mark style="color:red;"><strong>not coin denomination</strong></mark>). E.g. 110.50, 21500, 7999.99. Amount must be greater than 0 and less than ********. <br><br><mark style="color:red;"><strong>NOTE</strong></mark>: <mark style="color:red;">The amount you provide will always be rounded up to the nearest whole integer.</mark></td><td>Required</td></tr><tr><td>2</td><td>currency</td><td>This is the currency in which order value is to be processed. Default: <strong>NGN</strong>. Use other currency codes  e.g. <strong>USD</strong> if international payments are enabled for your account.</td><td>Optional</td></tr><tr><td>3</td><td>domain</td><td>Either <strong>sandbox</strong> or <strong>live</strong>. This determines whether the payment UI presents options for testing or options to accept live payments.</td><td>Required</td></tr><tr><td>4</td><td>key</td><td><p>Your merchant public key as copied from your VPay merchant account <strong>Settings -> API</strong> page. Use your merchant private key to authenticate server-side, web hook payloads. The private key is passed as a JWT token in the request header to your web hook as follows:<br><br><code>headers: {</code></p><p>    <code>'Content-Type': 'application/json',</code> </p><p>    <code>'x-payload-auth': JWT_TOKEN</code> </p><p><code>}</code><br><br>The deconstructed JWT token will contain a <strong>secret</strong> property with the private key as value.</p></td><td>Required</td></tr><tr><td>5</td><td>email</td><td>The customer email. A copy of successful payment receipts will be sent here. Default: <strong><EMAIL></strong></td><td>Optional</td></tr><tr><td>6</td><td>transactionref</td><td>A unique, alphanumeric transaction reference, to be generated by you each time you initiate a fresh transaction. Use your preferred scheme for generation.</td><td>Required</td></tr><tr><td>7</td><td>customer_service_channel</td><td>The customer service &#x26; support channels of your business e.g. <strong>Tel: +2348030070000, Email: <EMAIL></strong></td><td>Required</td></tr><tr><td>8</td><td>txn_charge</td><td>Amount to be charged to paying customer in addition to the order amount above. This amount must be greater than 0 and will share currency with order amount.</td><td>Optional</td></tr><tr><td>9</td><td>txn_charge_type</td><td>Either <strong>flat</strong> or <strong>percentage</strong>. This indicates how the payment total should be computed using the order amount and <strong>txn_charge</strong> above. E.g. order amount = 1000, txn_charge = 5, txn_charge_type = “percentage” then total = 1000 + (1000 * 5%) = 1000 + 50 = 1050.<br><br><mark style="color:red;">NOTE: To balance a VPay transaction fee of say, 1.3%, compute the txn_charge as follows.</mark><br><br><mark style="color:blue;">1) <strong>grand_total</strong> = amount / (1 - 1.3%) i.e. amount / (1 - 0.013)</mark><br><mark style="color:blue;">2) <strong>txn_charge</strong> = grand_total - amount</mark><br><br><mark style="color:blue;">E.g. to cover charges for 1,000, <strong>txn_charge</strong> should be (1000 / (1 - 0.013)) - 1000 i.e. 13.2 or 14 rounded up to next whole integer.</mark><br><br><mark style="color:red;">Ensure to set <strong>txn_charge_type</strong> to flat.</mark></td><td>Optional (if <strong>txn_charge</strong> is not specified)</td></tr><tr><td>10</td><td>onSuccess(response)</td><td><p>Use this to provide a Javascript callback function that will be called on your checkout webpage once the transaction is confirmed as successful. A status payload will be sent through the response argument in this format: {message: "<strong>Status Description"</strong>, code: "<strong>XX"</strong>}.<br><br><strong>Valid Responses Include:</strong></p><p></p><p><code>{</code></p><p>    <code>message: "</code><strong><code>Transaction successful.</code></strong>  </p><p>    <strong><code>amount: 1200"</code></strong><code>,</code></p><p>    <code>code: "</code><strong><code>00"</code></strong></p><p><code>}</code></p><p></p><p><em><mark style="color:red;"><strong>Recommended Practice</strong></mark></em></p><p><br><mark style="color:red;">When client-side onSuccess is called, ALWAYS confirm transaction status from your server side before processing customer order. This confirmation is made possible because your server side web</mark> <mark style="color:red;">hook is also notified of success.</mark></p></td><td>Optional (Not applicable to Mobile apps)</td></tr><tr><td>11</td><td>onExit(response)</td><td><p>Use this to provide a Javascript callback function that will be called once a user exits the dropin popup. A status payload will be sent through the response argument in this format: <br><br><code>{</code></p><p>    <code>message: "</code><strong><code>Status Descripton"</code></strong><code>,</code> </p><p>    <code>code: "</code><strong><code>XX"</code></strong></p><p><code>}</code><br><br><strong>Valid Responses Include:</strong><br></p><p><code>{</code></p><p>    <code>message: "Timeout occurred</code> </p><p>    <code>before transaction</code> </p><p>    <code>confirmation",</code> </p><p>    <code>code: "01"</code></p><p><code>}</code></p><p></p><p><code>{</code></p><p>    <code>message: "Timeout occurred</code>  </p><p>    <code>without user taking payment</code> </p><p>    <code>action",</code> </p><p>    <code>code: "02"</code></p><p><code>}</code></p><p></p><p><code>{</code></p><p>    <code>message: "User cancelled the</code> </p><p>    <code>transaction",</code></p><p>    <code>code: "03"</code></p><p><code>}</code></p><p></p><p><code>{</code></p><p>    <code>message: "User exited the flow",</code>  </p><p>    <code>code: "04"</code></p><p><code>}</code><br><br><code>{</code></p><p>    <code>message: "Failed transaction",</code> </p><p>    <code>code: "09"</code></p><p><code>}</code></p></td><td>Optional (Not applicable to Mobile apps)</td></tr></tbody></table>

---
description: >-
  This page goes into details of the authentication options available for your
  web hook.
---

# 6. Webhook Payload Authentication



There are two options available for payload verification:



**Option 1 – Secret Key Authentication**



Your webhook data payload will be sent along with a JWT token containing **{secret: your\_secret\_key}** as its payload. This token will be placed in the request header called **x-payload-auth**.



**Option 2 – IP Whitelisting**



It is recommended that you implement an IP whitelist to ensure that JSON payloads to your registered web hook are received only from the following authorized IPs.



IP1: Provided on request&#x20;

IP2: Provided on request



Transaction notification payloads will be re-tried if your web hook response status code is other than 200. Re-trial will take place twice per hour and then stop at end of day (EOD).

{% hint style="danger" %}
NOTE: The http method for your web hook should be POST as it will be receiving a JSON payload
{% endhint %}



## Webhook JSON Payload Samples



Bank Transfer (EFT)

```json
{
    "reference": "Smooth-Test-5cb7311e-efc2-e2dd-9e94-fvvb4a76932b",
    "session_id": "100089040525101154000562534883",
    "amount": 100,
    "fee": 1.17,
    "account_number": "**********",
    "originator_account_number": "",
    "originator_account_name": "Emeka Ajibade",
    "originator_bank": "",
    "timestamp": "2023-06-07T09:26:30.214Z",
    "email": "<EMAIL>",
    "transactionref": "vpayuserd9e34cff027a11877416861824430"
}
```



Card

```json
{
    "reference": "Smooth-Test-5cb7311e-1b93-665f-9e94-5d704a76932b",
    "session_id": "Card-VPay-************",
    "amount": 90,
    "fee": 1.17,
    "originator_account_name": "Card Payment By ****7030 (<EMAIL>)",
    "timestamp": "2023-06-07T09:26:30.214Z",
    "email": "<EMAIL>",
    "transactionref": "vpayuser64804cff027a1187741686129919",
    "transaction_status": "success" | "failed"
}
```



{% hint style="danger" %}
NOTE: The **transaction\_status** property is either success or failed for Card payloads.
{% endhint %}

---
description: >-
  This page goes into details of the authentication options available for your
  web hook.
---

# 6. Webhook Payload Authentication



There are two options available for payload verification:



**Option 1 – Secret Key Authentication**



Your webhook data payload will be sent along with a JWT token containing **{secret: your\_secret\_key}** as its payload. This token will be placed in the request header called **x-payload-auth**.



**Option 2 – IP Whitelisting**



It is recommended that you implement an IP whitelist to ensure that JSON payloads to your registered web hook are received only from the following authorized IPs.



IP1: Provided on request&#x20;

IP2: Provided on request



Transaction notification payloads will be re-tried if your web hook response status code is other than 200. Re-trial will take place twice per hour and then stop at end of day (EOD).

{% hint style="danger" %}
NOTE: The http method for your web hook should be POST as it will be receiving a JSON payload
{% endhint %}



## Webhook JSON Payload Samples



Bank Transfer (EFT)

```json
{
    "reference": "Smooth-Test-5cb7311e-efc2-e2dd-9e94-fvvb4a76932b",
    "session_id": "100089040525101154000562534883",
    "amount": 100,
    "fee": 1.17,
    "account_number": "**********",
    "originator_account_number": "",
    "originator_account_name": "Emeka Ajibade",
    "originator_bank": "",
    "timestamp": "2023-06-07T09:26:30.214Z",
    "email": "<EMAIL>",
    "transactionref": "vpayuserd9e34cff027a11877416861824430"
}
```



Card

```json
{
    "reference": "Smooth-Test-5cb7311e-1b93-665f-9e94-5d704a76932b",
    "session_id": "Card-VPay-************",
    "amount": 90,
    "fee": 1.17,
    "originator_account_name": "Card Payment By ****7030 (<EMAIL>)",
    "timestamp": "2023-06-07T09:26:30.214Z",
    "email": "<EMAIL>",
    "transactionref": "vpayuser64804cff027a1187741686129919",
    "transaction_status": "success" | "failed"
}
```



{% hint style="danger" %}
NOTE: The **transaction\_status** property is either success or failed for Card payloads.
{% endhint %}

