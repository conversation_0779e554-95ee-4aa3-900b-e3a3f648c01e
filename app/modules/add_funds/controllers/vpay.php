<?php
defined('BASEPATH') or exit('No direct script access allowed');

class vpay extends MX_Controller
{
    public $tb_users;
    public $tb_transaction_logs;
    public $tb_payments;
    public $tb_payments_bonuses;
    public $payment_type;
    public $payment_id;
    public $currency_code;
    public $public_key;
    public $secret_key;
    public $environment;
    public $take_fee_from_user;

    public function __construct($payment = "")
    {
        parent::__construct();
        $this->load->model('add_funds_model', 'model');

        $this->tb_users = USERS;
        $this->payment_type = 'vpay';
        $this->tb_transaction_logs = TRANSACTION_LOGS;
        $this->tb_payments = PAYMENTS_METHOD;
        $this->tb_payments_bonuses = PAYMENTS_BONUSES;
        // Vpay supports NGN (Nigerian Naira) primarily
        $this->currency_code = 'NGN';

        if (!$payment) {
            $payment = $this->model->get('id, type, name, params', $this->tb_payments, ['type' => $this->payment_type]);
        }
        
        if ($payment) {
            $this->payment_id = $payment->id;
            $params = $payment->params;
            $option = get_value($params, 'option');
            $this->take_fee_from_user = get_value($params, 'take_fee_from_user');
            //options
            $this->public_key = get_value($option, 'public_key');
            $this->secret_key = get_value($option, 'secret_key');
            $this->environment = get_value($option, 'environment', 'sandbox');
        }
    }

    public function index()
    {
        redirect(cn("add_funds"));
    }

    /**
     * Process payment
     */
    public function process()
    {
        if (!$this->input->is_ajax_request()) {
            redirect(cn("add_funds"));
        }

        $users = $this->model->get('id, email, first_name, last_name', $this->tb_users, ['id' => session('uid')]);
        if (!$users) {
            _validation('error', lang('There_was_an_error_processing_your_request_Please_try_again_later'));
        }

        // Validate form data
        $this->form_validation->set_rules('amount', 'Amount', 'trim|required|numeric|greater_than[0]');
        $this->form_validation->set_rules('agree', 'Agreement', 'trim|required|in_list[1]');
        
        if (!$this->form_validation->run()) {
            _validation('error', validation_errors());
        }

        $amount = (float)$this->input->post('amount');
        $payment_id = (int)$this->input->post('payment_id');

        // Get payment method details
        $payment_method = $this->model->get('*', $this->tb_payments, ['id' => $payment_id, 'type' => $this->payment_type]);
        if (!$payment_method || $payment_method->status != 1) {
            _validation('error', lang('Payment method is not available'));
        }

        // Validate amount limits
        if ($amount < $payment_method->min || $amount > $payment_method->max) {
            _validation('error', sprintf(lang('Amount must be between %s and %s'), $payment_method->min, $payment_method->max));
        }

        // Calculate fees
        $params = json_decode($payment_method->params, true);
        $option = $params['option'] ?? [];
        $tnx_fee = (float)($option['tnx_fee'] ?? 0);
        
        if ($this->take_fee_from_user) {
            $total_amount = $amount + $tnx_fee;
        } else {
            $total_amount = $amount;
        }

        // Generate unique transaction reference
        $reference = 'vpay_' . time() . '_' . session('uid') . '_' . rand(1000, 9999);

        // Create transaction log
        $data_tnx_log = array(
            "ids" => ids(),
            "uid" => session('uid'),
            "type" => $this->payment_type,
            "transaction_id" => $reference,
            "amount" => $amount,
            "txn_fee" => $this->take_fee_from_user ? $tnx_fee : 0,
            "status" => 0,
            "created" => NOW,
        );

        $this->db->insert($this->tb_transaction_logs, $data_tnx_log);
        $transaction_log_id = $this->db->insert_id();

        if (!$transaction_log_id) {
            log_message('error', 'Vpay payment failed - Could not create transaction log. DB Error: ' . $this->db->error()['message']);
            _validation('error', lang('There_was_an_error_processing_your_request_Please_try_again_later'));
        }

        // Prepare Vpay payment data
        $vpay_data = array(
            'amount' => $total_amount,
            'currency' => $this->currency_code,
            'domain' => $this->environment,
            'key' => $this->public_key,
            'email' => $users->email,
            'transactionref' => $reference,
            'customer_service_channel' => get_option('website_name') . ' Support, Email: ' . get_option('admin_email'),
            'txn_charge' => $this->take_fee_from_user ? $tnx_fee : 0,
            'txn_charge_type' => 'flat',
            'metadata' => array(
                'user_id' => session('uid'),
                'transaction_log_id' => $transaction_log_id,
                'website' => get_option('website_name')
            )
        );

        log_message('info', 'Vpay payment initialized - Reference: ' . $reference);
        
        // Return data for frontend JavaScript to handle
        if ($this->input->is_ajax_request()) {
            ms([
                'status' => 'success', 
                'vpay_data' => $vpay_data,
                'callback_url' => cn("add_funds/vpay/callback"),
                'base_url' => $this->environment === 'live' ? 'https://dropin.vpay.africa' : 'https://dropin-sandbox.vpay.africa'
            ]);
        }
    }

    /**
     * Handle Vpay callback
     */
    public function callback()
    {
        $reference = $this->input->get('reference');
        
        if (!$reference) {
            redirect(cn("add_funds/unsuccess"));
        }

        // Get transaction from database
        $transaction = $this->model->get('*', $this->tb_transaction_logs, [
            'transaction_id' => $reference, 
            'type' => $this->payment_type,
            'status' => 0
        ]);

        if (!$transaction) {
            redirect(cn("add_funds/unsuccess"));
        }

        // For Vpay, we rely on webhook for final confirmation
        // This callback is just for user experience
        redirect(cn("add_funds/success"));
    }

    /**
     * Handle webhook
     */
    public function webhook()
    {
        // Get the body of the request
        $input = @file_get_contents("php://input");
        $event = json_decode($input, true);

        // Verify the webhook signature
        $signature = $_SERVER['HTTP_X_PAYLOAD_AUTH'] ?? '';
        if (!$this->verify_webhook_signature($input, $signature)) {
            log_message('error', 'Vpay webhook signature verification failed');
            http_response_code(400);
            exit();
        }

        if ($event && isset($event['transactionref'])) {
            $reference = $event['transactionref'];
            
            // Check if this is a successful transaction
            if (isset($event['transaction_status']) && $event['transaction_status'] === 'success') {
                $this->process_successful_payment($event);
            } else {
                log_message('info', 'Vpay webhook received for failed/pending transaction: ' . $reference);
            }
        }

        http_response_code(200);
        echo "OK";
    }

    /**
     * Verify webhook signature using JWT
     */
    private function verify_webhook_signature($payload, $signature)
    {
        if (empty($signature) || empty($this->secret_key)) {
            return false;
        }

        try {
            // For JWT verification, you would need to implement JWT decoding
            // This is a simplified version - in production, use a proper JWT library
            $decoded = base64_decode(str_replace(['_', '-'], ['+', '/'], explode('.', $signature)[1]));
            $jwt_payload = json_decode($decoded, true);
            
            return isset($jwt_payload['secret']) && $jwt_payload['secret'] === $this->secret_key;
        } catch (Exception $e) {
            log_message('error', 'Vpay JWT verification error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Process successful payment
     */
    private function process_successful_payment($payment_data)
    {
        $reference = $payment_data['transactionref'];
        $amount = (float)$payment_data['amount'];
        
        // Get transaction from database
        $transaction = $this->model->get('*', $this->tb_transaction_logs, [
            'transaction_id' => $reference, 
            'type' => $this->payment_type,
            'status' => 0
        ]);

        if (!$transaction) {
            log_message('error', 'Vpay webhook: Transaction not found - ' . $reference);
            return false;
        }

        // Update transaction status
        $this->db->update($this->tb_transaction_logs, [
            'status' => 1,
            'changed' => NOW
        ], ['id' => $transaction->id]);

        // Add funds to user account
        $this->model->add_funds_bonus_email($transaction, $this->payment_id);

        log_message('info', 'Vpay payment completed successfully - Reference: ' . $reference);
        return true;
    }
}
