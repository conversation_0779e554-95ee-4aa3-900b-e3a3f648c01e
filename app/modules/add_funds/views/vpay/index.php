<?php
  $option                 = get_value($payment_params, 'option');
  $min_amount             = get_value($payment_params, 'min');
  $max_amount             = get_value($payment_params, 'max');
  $type                   = get_value($payment_params, 'type');
  $tnx_fee                = get_value($option, 'tnx_fee');
  $currency_rate_to_usd   = get_value($option, 'rate_to_usd');
?>

<div class="add-funds-form-content">
  <form class="form actionAddFundsForm" action="#" method="POST">
    <div class="row">
      <div class="col-md-12">

        <div class="for-group text-center">
          <img src="<?=BASE?>/assets/images/payments/vpay.png" style="max-width: 250px;" alt="Vpay icon">
          <p class="p-t-10"><small><?=sprintf(lang("you_can_deposit_funds_with_paypal_they_will_be_automaticly_added_into_your_account"), 'Vpay')?></small></p>
        </div>

        <div class="form-group">
            <label><?=sprintf(lang("amount_usd"), 'NGN'); ?></label>
            <input class="form-control square" type="number" name="amount" placeholder="<?php echo $min_amount; ?>">
          </div>                      

          <div class="form-group">
            <label><?=lang("transaction_fee")?></label>
            <p class="form-control-static"><?php echo $tnx_fee; ?> NGN</p>
          </div>

          <?php if($currency_rate_to_usd != ""){ ?>
          <div class="form-group">
            <label><?=lang("exchange_rate")?></label>
            <p class="form-control-static">1 USD = <?php echo $currency_rate_to_usd; ?> NGN</p>
          </div>
          <?php } ?>

          <div class="form-group">
            <label><?=lang("you_have_to_pay")?></label>
            <p class="form-control-static"><span class="payment-amount-preview">0</span> NGN</p>
          </div>

          <div class="form-group">
            <label class="custom-control custom-checkbox">
              <input type="checkbox" class="custom-control-input" name="agree" value="1">
              <span class="custom-control-label text-uppercase"><strong><?=lang("yes_i_understand_after_the_funds_added_i_will_not_ask_fraudulent_dispute_or_chargeback")?></strong></span>
            </label>
          </div>
          
          <div class="form-actions left">
            <input type="hidden" name="payment_id" value="<?php echo $payment_id; ?>">
            <input type="hidden" name="payment_method" value="<?php echo $type; ?>">
            <button type="submit" class="btn round btn-primary btn-min-width mr-1 mb-1">
              <?=lang("Pay")?>
            </button>
          </div>

      </div>  
    </div>
  </form>
</div>

<script>
$(document).ready(function() {
    var tnx_fee = <?php echo $tnx_fee; ?>;
    var take_fee_from_user = <?php echo get_value($option, 'take_fee_from_user', 0); ?>;
    
    $('input[name="amount"]').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var total = take_fee_from_user ? amount + tnx_fee : amount;
        $('.payment-amount-preview').text(total.toFixed(2));
    });
    
    // Trigger initial calculation
    $('input[name="amount"]').trigger('input');
});
</script>
