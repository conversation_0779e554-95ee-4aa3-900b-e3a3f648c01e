<section class="add-funds m-t-30">   
  <div class="container-fluid">
    <div class="row justify-content-md-center" id="result_ajaxSearch">
      <div class="col-md-5">
        <div class="card">
          <div class="card-header d-flex align-items-center">
            <h3 class="card-title"> Vpay Integration</h3>
          </div>
          <div class="card-body">
            <div class="tab-content">
              <form id="paymentFrm" method="post" action="<?=cn($module."/process")?>">

                <div class="form-group">
                  <label class="form-label"><?=sprintf(lang("total_amount_XX_includes_fee"), 'NGN')?></label>
                  <input type="text" class="form-control" value="<?=$amount?>" readonly>
                </div>

                <div class="form-group">
                  <label class="form-label"><?=lang("Email")?></label>
                  <input type="email" class="form-control" value="<?=$user_email?>" readonly>
                </div>

                <div class="form-group">
                  <label class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="agree" value="1" required>
                    <span class="custom-control-label text-uppercase"><strong><?=lang("yes_i_understand_after_the_funds_added_i_will_not_ask_fraudulent_dispute_or_chargeback")?></strong></span>
                  </label>
                </div>

                <input type="hidden" name="payment_id" value="<?=$payment_id?>">
                <input type="hidden" name="amount" value="<?=$amount?>">
                
                <div class="form-actions">
                  <button type="submit" class="btn btn-primary btn-block">
                    <i class="fa fa-credit-card"></i> <?=lang("Pay_with_Vpay")?>
                  </button>
                </div>

              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
$(document).ready(function() {
    $('#paymentFrm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();
        
        // Disable submit button
        submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Processing...');
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Initialize Vpay payment
                    initializeVpayPayment(response.vpay_data, response.base_url, response.callback_url);
                } else {
                    show_message(response.message, 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function() {
                show_message('An error occurred. Please try again.', 'error');
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});

function initializeVpayPayment(vpayData, baseUrl, callbackUrl) {
    // Load Vpay script dynamically
    var script = document.createElement('script');
    script.src = baseUrl + '/dropin/v1/initialise.js';
    script.onload = function() {
        if (window.VPayDropin) {
            var options = {
                amount: vpayData.amount,
                currency: vpayData.currency,
                domain: vpayData.domain,
                key: vpayData.key,
                email: vpayData.email,
                transactionref: vpayData.transactionref,
                customer_service_channel: vpayData.customer_service_channel,
                txn_charge: vpayData.txn_charge,
                txn_charge_type: vpayData.txn_charge_type,
                onSuccess: function(response) {
                    console.log('Vpay payment successful:', response);
                    // Redirect to callback URL
                    window.location.href = callbackUrl + '?reference=' + vpayData.transactionref;
                },
                onExit: function(response) {
                    console.log('Vpay payment exited:', response);
                    // Re-enable the submit button
                    $('#paymentFrm button[type="submit"]').prop('disabled', false).html('<i class="fa fa-credit-card"></i> <?=lang("Pay_with_Vpay")?>');
                    
                    // Show appropriate message based on exit reason
                    if (response.code === '03') {
                        show_message('Payment was cancelled by user.', 'warning');
                    } else if (response.code === '09') {
                        show_message('Payment failed. Please try again.', 'error');
                    } else {
                        show_message('Payment was not completed. Please try again.', 'warning');
                    }
                }
            };
            
            var {open, exit} = VPayDropin.create(options);
            open();
        } else {
            show_message('Failed to load Vpay payment system. Please try again.', 'error');
            $('#paymentFrm button[type="submit"]').prop('disabled', false).html('<i class="fa fa-credit-card"></i> <?=lang("Pay_with_Vpay")?>');
        }
    };
    script.onerror = function() {
        show_message('Failed to load Vpay payment system. Please try again.', 'error');
        $('#paymentFrm button[type="submit"]').prop('disabled', false).html('<i class="fa fa-credit-card"></i> <?=lang("Pay_with_Vpay")?>');
    };
    document.head.appendChild(script);
}

function show_message(message, type) {
    // Implement your message display function here
    // This should match your existing notification system
    if (typeof ms !== 'undefined') {
        ms({status: type, message: message});
    } else {
        alert(message);
    }
}
</script>
