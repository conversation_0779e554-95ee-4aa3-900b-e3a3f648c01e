<?php
  $payment_elements = [
    [
      'label'      => form_label('Environment'),
      'element'    => form_dropdown('payment_params[option][environment]', $form_environment, @$payment_option->environment, ['class' => $class_element]),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
    ],
    [
      'label'      => form_label('Public Key'),
      'element'    => form_input(['name' => "payment_params[option][public_key]", 'value' => @$payment_option->public_key, 'type' => 'text', 'class' => $class_element]),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
    ],
    [
      'label'      => form_label('Secret Key'),
      'element'    => form_input(['name' => "payment_params[option][secret_key]", 'value' => @$payment_option->secret_key, 'type' => 'text', 'class' => $class_element]),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
    ],
    [
      'label'      => form_label('Transaction fee (NGN)'),
      'element'    => form_input(['name' => "payment_params[option][tnx_fee]", 'value' => @$payment_option->tnx_fee, 'type' => 'text', 'class' => $class_element . ' text-right']),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
    ],
    [
      'label'      => form_label('Currency rate'),
      'element'    => form_input(['name' => "payment_params[option][rate_to_usd]", 'value' => @$payment_option->rate_to_usd, 'type' => 'text', 'class' => $class_element . ' text-right']),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
      'type'       => "exchange_option",
      'item1'      => ['name' => get_option('currecy_code', 'USD'), 'value' => 1],
      'item2'      => ['name' => 'NGN', 'value' => 1540],
    ],
    [
      'label'      => form_label('Take fee from user'),
      'element'    => form_dropdown('payment_params[option][take_fee_from_user]', [0 => 'No', 1 => 'Yes'], @$payment_option->take_fee_from_user, ['class' => $class_element]),
      'class_main' => "col-md-12 col-sm-12 col-xs-12",
    ],
  ];
  echo render_elements_form($payment_elements);
?>

<div class="form-group">
  <label for="">Setup Instructions:</label>
  <ol>
    <li>Create a Vpay merchant account at <a href="https://vpay.africa" target="_blank">https://vpay.africa</a></li>
    <li>Get your Public Key and Secret Key from your Vpay merchant dashboard under <strong>Settings -> API</strong></li>
    <li>Set the webhook URL in your Vpay dashboard to: <code><?=cn('add_funds/vpay/webhook')?></code></li>
    <li>Choose your environment (Sandbox for testing, Live for production)</li>
    <li>Enter your API keys above and set the transaction fee</li>
    <li>Set the currency rate (NGN to USD) - <strong>Enter as plain number without commas (e.g., 1540 not 1,540)</strong></li>
    <li>Choose whether to take the transaction fee from the user or absorb it</li>
    <li>Enable the payment method when ready</li>
  </ol>
</div>

<div class="form-group">
  <label for="">Important Notes:</label>
  <ul>
    <li><strong>Webhook Authentication:</strong> Vpay uses JWT tokens in the <code>x-payload-auth</code> header for webhook authentication</li>
    <li><strong>Supported Currency:</strong> Vpay primarily supports NGN (Nigerian Naira)</li>
    <li><strong>Transaction Fees:</strong> You can either absorb the transaction fee or pass it to the user</li>
    <li><strong>Testing:</strong> Use Sandbox environment for testing before going live</li>
    <li><strong>Customer Support:</strong> Make sure to provide valid customer service channels in your configuration</li>
  </ul>
</div>

<div class="form-group">
  <label for="">Webhook Payload Sample:</label>
  <pre><code>{
  "reference": "Smooth-Test-5cb7311e-1b93-665f-9e94-5d704a76932b",
  "session_id": "Card-VPay-************",
  "amount": 90,
  "fee": 1.17,
  "originator_account_name": "Card Payment By ****7030 (<EMAIL>)",
  "timestamp": "2023-06-07T09:26:30.214Z",
  "email": "<EMAIL>",
  "transactionref": "vpay_1686129919_123_4567",
  "transaction_status": "success"
}</code></pre>
</div>
