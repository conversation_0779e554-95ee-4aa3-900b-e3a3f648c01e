<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_vpay_payment_method extends CI_Migration {

    public function up()
    {
        // Check if Vpay payment method already exists
        $existing = $this->db->get_where('payments', array('type' => 'vpay'))->row();
        
        if (!$existing) {
            // Get the next sort order
            $max_sort = $this->db->select_max('sort')->get('payments')->row()->sort;
            $next_sort = ($max_sort ? $max_sort : 0) + 1;
            
            // Prepare the parameters JSON
            $params = json_encode([
                'type' => 'vpay',
                'min' => '10',
                'max' => '10000',
                'new_users' => '1',
                'status' => '0',
                'option' => [
                    'environment' => 'sandbox',
                    'public_key' => '',
                    'secret_key' => '',
                    'tnx_fee' => '0',
                    'rate_to_usd' => '1540',
                    'take_fee_from_user' => '0'
                ]
            ]);
            
            // Insert Vpay payment method
            $data = array(
                'ids' => substr(md5(uniqid(rand(), true)), 0, 32),
                'type' => 'vpay',
                'name' => 'Vpay',
                'params' => $params,
                'min' => 10,
                'max' => 10000,
                'new_users' => 1,
                'status' => 0,
                'sort' => $next_sort,
                'created' => date('Y-m-d H:i:s'),
                'changed' => date('Y-m-d H:i:s')
            );
            
            $this->db->insert('payments', $data);
            
            echo "Vpay payment method added successfully.\n";
        } else {
            echo "Vpay payment method already exists.\n";
        }
    }

    public function down()
    {
        // Remove Vpay payment method
        $this->db->delete('payments', array('type' => 'vpay'));
        
        echo "Vpay payment method removed successfully.\n";
    }
}
