<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_vpay_payment_method extends CI_Migration {

    public function up()
    {
        // Determine the correct table name
        $table_name = 'payments';
        if (!$this->db->table_exists($table_name)) {
            $table_name = 'payments_method';
            if (!$this->db->table_exists($table_name)) {
                echo "Error: Neither 'payments' nor 'payments_method' table exists.\n";
                return;
            }
        }

        // Check if Vpay payment method already exists
        $existing = $this->db->get_where($table_name, array('type' => 'vpay'))->row();

        if (!$existing) {
            // Get table structure to check available columns
            $fields = $this->db->list_fields($table_name);

            // Get the next sort order
            $max_sort = $this->db->select_max('sort')->get($table_name)->row()->sort;
            $next_sort = ($max_sort ? $max_sort : 0) + 1;

            // Prepare the parameters JSON
            $params = json_encode([
                'type' => 'vpay',
                'min' => '10',
                'max' => '10000',
                'new_users' => '1',
                'status' => '0',
                'option' => [
                    'environment' => 'sandbox',
                    'public_key' => '',
                    'secret_key' => '',
                    'tnx_fee' => '0',
                    'rate_to_usd' => '1540',
                    'take_fee_from_user' => '0'
                ]
            ]);

            // Build data array based on available columns
            $data = array(
                'type' => 'vpay',
                'name' => 'Vpay',
                'params' => $params,
                'min' => 10,
                'max' => 10000,
                'new_users' => 1,
                'status' => 0,
                'sort' => $next_sort
            );

            // Add optional columns if they exist
            if (in_array('ids', $fields)) {
                $data['ids'] = substr(md5(uniqid(rand(), true)), 0, 32);
            }

            if (in_array('created', $fields)) {
                $data['created'] = date('Y-m-d H:i:s');
            }

            if (in_array('changed', $fields)) {
                $data['changed'] = date('Y-m-d H:i:s');
            }

            $this->db->insert($table_name, $data);

            echo "Vpay payment method added successfully to table '$table_name'.\n";
        } else {
            echo "Vpay payment method already exists.\n";
        }
    }

    public function down()
    {
        // Determine the correct table name
        $table_name = 'payments';
        if (!$this->db->table_exists($table_name)) {
            $table_name = 'payments_method';
        }

        // Remove Vpay payment method
        $this->db->delete($table_name, array('type' => 'vpay'));

        echo "Vpay payment method removed successfully from table '$table_name'.\n";
    }
}
